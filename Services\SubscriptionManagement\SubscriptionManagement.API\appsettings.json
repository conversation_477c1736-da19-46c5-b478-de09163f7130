{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=TLI_SubscriptionManagement;Username=postgres;Password=************", "Redis": "localhost:6379"}, "JwtSettings": {"Secret": "your-super-secret-key-that-is-at-least-32-characters-long", "Issuer": "TLI-Identity-Service", "Audience": "TLI-Services", "ExpiryMinutes": 60}, "RabbitMQ": {"Host": "localhost", "Port": 5672, "Username": "guest", "Password": "guest"}, "RazorPay": {"KeyId": "your_razorpay_key_id", "KeySecret": "your_razorpay_key_secret", "WebhookSecret": "your_webhook_secret"}, "Services": {"Identity": {"BaseUrl": "http://**************:5005/"}}}