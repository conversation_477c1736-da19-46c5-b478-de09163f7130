using System;
using System.Collections.Generic;
using MediatR;

namespace Identity.Application.Permissions.Queries.GetAllPermissionsFromDb
{
    public class GetAllPermissionsFromDbQuery : IRequest<GetAllPermissionsFromDbResponse>
    {
        public string? Category { get; set; }
        public string? SearchTerm { get; set; }
        public bool? IsActive { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 100; // Default to 100 to get all permissions
    }

    public class GetAllPermissionsFromDbResponse
    {
        public IEnumerable<PermissionDto> Permissions { get; set; } = new List<PermissionDto>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

    public class PermissionDto
    {
        public Guid Id { get; set; }
        public Guid? FeatureId { get; set; }
        public string? FeatureName { get; set; }
        public Guid? MenuId { get; set; }
        public string? MenuName { get; set; }
        public string PermissionName { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;

        // Backward compatibility fields
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Resource { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
