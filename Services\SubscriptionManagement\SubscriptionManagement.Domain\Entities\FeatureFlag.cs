using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Entities;

public class FeatureFlag : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Key { get; private set; }
    public FeatureFlagType Type { get; private set; }
    public FeatureFlagStatus Status { get; private set; }
    public DateTime? StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public int RolloutPercentage { get; private set; }
    public string? TargetAudience { get; private set; }
    public string? ABTestConfiguration { get; private set; }
    public string? DefaultValue { get; private set; }
    public string? Variants { get; private set; } // JSON string for A/B test variants
    public Guid? MenuId { get; private set; } // Associated menu ID from UserManagement service
    public Dictionary<string, object>? Metadata { get; private set; }
    public List<FeatureFlagRule> Rules { get; private set; }
    public List<FeatureFlagUsage> UsageHistory { get; private set; }

    private FeatureFlag()
    {
        Rules = new List<FeatureFlagRule>();
        UsageHistory = new List<FeatureFlagUsage>();
        Metadata = null; // Make metadata optional
    }

    public FeatureFlag(
        string name,
        string description,
        string key,
        FeatureFlagType type,
        string? defaultValue = null,
        Guid? menuId = null) : this()
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Key = key ?? throw new ArgumentNullException(nameof(key));
        Type = type;
        Status = FeatureFlagStatus.Draft;
        RolloutPercentage = 0;
        DefaultValue = defaultValue;
        MenuId = menuId;
    }

    public void Activate(DateTime? startDate = null, DateTime? endDate = null)
    {
        if (Status == FeatureFlagStatus.Archived)
            throw new InvalidOperationException("Cannot activate an archived feature flag");

        Status = FeatureFlagStatus.Active;
        StartDate = startDate ?? DateTime.UtcNow;
        EndDate = endDate;
    }

    public void Deactivate()
    {
        Status = FeatureFlagStatus.Inactive;
    }

    public void Archive()
    {
        Status = FeatureFlagStatus.Archived;
        EndDate = DateTime.UtcNow;
    }

    public void UpdateRolloutPercentage(int percentage)
    {
        if (percentage < 0 || percentage > 100)
            throw new ArgumentException("Rollout percentage must be between 0 and 100");

        RolloutPercentage = percentage;
    }

    public void SetTargetAudience(string audience)
    {
        TargetAudience = audience;
    }

    public void ConfigureABTest(string configuration, string variants)
    {
        if (Type != FeatureFlagType.ABTest)
            throw new InvalidOperationException("A/B test configuration can only be set for A/B test feature flags");

        ABTestConfiguration = configuration;
        Variants = variants;
    }

    public void AddRule(FeatureFlagRule rule)
    {
        Rules.Add(rule);
    }

    public void RemoveRule(Guid ruleId)
    {
        var rule = Rules.FirstOrDefault(r => r.Id == ruleId);
        if (rule != null)
        {
            Rules.Remove(rule);
        }
    }

    public void RecordUsage(Guid userId, string? variant = null, Dictionary<string, object>? context = null)
    {
        var usage = new FeatureFlagUsage(Id, userId, variant, context);
        UsageHistory.Add(usage);
    }

    public bool IsActiveForUser(Guid userId, Dictionary<string, object>? context = null)
    {
        if (Status != FeatureFlagStatus.Active)
            return false;

        if (StartDate.HasValue && DateTime.UtcNow < StartDate.Value)
            return false;

        if (EndDate.HasValue && DateTime.UtcNow > EndDate.Value)
            return false;

        // Check rollout percentage
        if (RolloutPercentage < 100)
        {
            var hash = GetUserHash(userId);
            if (hash > RolloutPercentage)
                return false;
        }

        // Check rules
        foreach (var rule in Rules.Where(r => r.IsActive))
        {
            if (!rule.EvaluateRule(userId, context))
                return false;
        }

        return true;
    }

    public string? GetVariantForUser(Guid userId, Dictionary<string, object>? context = null)
    {
        if (!IsActiveForUser(userId, context))
            return DefaultValue;

        if (Type == FeatureFlagType.ABTest && !string.IsNullOrEmpty(Variants))
        {
            // Simple A/B test variant selection based on user hash
            var hash = GetUserHash(userId);
            var variants = System.Text.Json.JsonSerializer.Deserialize<string[]>(Variants);

            if (variants != null && variants.Length > 0)
            {
                var variantIndex = hash % variants.Length;
                return variants[variantIndex];
            }
        }

        return DefaultValue;
    }

    private int GetUserHash(Guid userId)
    {
        // Create a consistent hash for the user for this feature flag
        var combined = $"{userId}:{Key}";
        return Math.Abs(combined.GetHashCode()) % 100;
    }

    // Additional methods for enhanced feature flag functionality
    public void SetType(FeatureFlagType type)
    {
        Type = type;
    }

    public void SetDefaultValue(string? defaultValue)
    {
        DefaultValue = defaultValue;
    }

    public void SetStatus(FeatureFlagStatus status)
    {
        Status = status;
    }

    public void SetRolloutPercentage(int percentage)
    {
        UpdateRolloutPercentage(percentage);
    }

    public void SetActivationPeriod(DateTime? startDate, DateTime? endDate)
    {
        StartDate = startDate;
        EndDate = endDate;
    }

    public void UpdateName(string name)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
    }

    public void UpdateDescription(string description)
    {
        Description = description ?? throw new ArgumentNullException(nameof(description));
    }

    public void UpdateConfiguration(Dictionary<string, object> newMetadata)
    {
        if (Metadata == null)
        {
            Metadata = new Dictionary<string, object>();
        }

        foreach (var item in newMetadata)
        {
            Metadata[item.Key] = item.Value;
        }
    }

    public void Enable()
    {
        if (Status == FeatureFlagStatus.Draft)
        {
            Status = FeatureFlagStatus.Active;
        }
    }

    public void Disable()
    {
        if (Status == FeatureFlagStatus.Active)
        {
            Status = FeatureFlagStatus.Inactive;
        }
    }

    public bool IsEnabled => Status == FeatureFlagStatus.Active;

    public bool IsInRolloutPeriod()
    {
        var now = DateTime.UtcNow;

        if (StartDate.HasValue && now < StartDate.Value)
            return false;

        if (EndDate.HasValue && now > EndDate.Value)
            return false;

        return true;
    }

    public double GetCurrentRolloutReach()
    {
        return RolloutPercentage / 100.0;
    }

    public bool CanUserAccessFeature(Guid userId, Dictionary<string, object>? context = null)
    {
        return IsActiveForUser(userId, context);
    }

    public void UpdateMetadata(string key, object value)
    {
        if (Metadata == null)
        {
            Metadata = new Dictionary<string, object>();
        }
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key)
    {
        if (Metadata?.TryGetValue(key, out var value) == true && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    public void SetMenuId(Guid? menuId)
    {
        MenuId = menuId;
    }
}
