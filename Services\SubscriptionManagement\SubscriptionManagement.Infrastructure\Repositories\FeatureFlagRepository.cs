using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories;

public class FeatureFlagRepository : IFeatureFlagRepository
{
    private readonly SubscriptionDbContext _context;

    public FeatureFlagRepository(SubscriptionDbContext context)
    {
        _context = context;
    }

    public async Task<FeatureFlag?> GetByIdAsync(Guid id)
    {
        return await _context.FeatureFlags
            .Include(f => f.Rules)
            .Include(f => f.UsageHistory)
            .FirstOrDefaultAsync(f => f.Id == id);
    }

    public async Task<FeatureFlag?> GetByKeyAsync(string key)
    {
        return await _context.FeatureFlags
            .Include(f => f.Rules)
            .Include(f => f.UsageHistory)
            .FirstOrDefaultAsync(f => f.Key == key);
    }

    public async Task<List<FeatureFlag>> GetAllAsync()
    {
        return await _context.FeatureFlags
            .Include(f => f.Rules)
            .Include(f => f.UsageHistory)
            .OrderBy(f => f.Name)
            .ToListAsync();
    }

    public async Task<List<FeatureFlag>> GetActiveAsync()
    {
        return await _context.FeatureFlags
            .Include(f => f.Rules)
            .Include(f => f.UsageHistory)
            .Where(f => f.Status == FeatureFlagStatus.Active)
            .OrderBy(f => f.Name)
            .ToListAsync();
    }

    public async Task<List<FeatureFlag>> GetByTypeAsync(FeatureFlagType type)
    {
        return await _context.FeatureFlags
            .Include(f => f.Rules)
            .Include(f => f.UsageHistory)
            .Where(f => f.Type == type)
            .OrderBy(f => f.Name)
            .ToListAsync();
    }

    public async Task<List<FeatureFlag>> GetByStatusAsync(FeatureFlagStatus status)
    {
        return await _context.FeatureFlags
            .Include(f => f.Rules)
            .Include(f => f.UsageHistory)
            .Where(f => f.Status == status)
            .OrderBy(f => f.Name)
            .ToListAsync();
    }

    public async Task<List<FeatureFlag>> GetForUserAsync(Guid userId, Dictionary<string, object>? context = null)
    {
        var activeFlags = await GetActiveAsync();

        return activeFlags
            .Where(f => f.IsActiveForUser(userId, context))
            .ToList();
    }

    public async Task<FeatureFlag> AddAsync(FeatureFlag featureFlag)
    {
        _context.FeatureFlags.Add(featureFlag);
        await _context.SaveChangesAsync();
        return featureFlag;
    }

    public async Task UpdateAsync(FeatureFlag featureFlag)
    {
        _context.FeatureFlags.Update(featureFlag);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var featureFlag = await _context.FeatureFlags.FindAsync(id);
        if (featureFlag != null)
        {
            _context.FeatureFlags.Remove(featureFlag);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<List<FeatureFlagUsage>> GetUsageHistoryAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null)
    {
        var query = _context.FeatureFlagUsages
            .Where(u => u.FeatureFlagId == featureFlagId);

        if (from.HasValue)
        {
            query = query.Where(u => u.AccessedAt >= from.Value);
        }

        if (to.HasValue)
        {
            query = query.Where(u => u.AccessedAt <= to.Value);
        }

        return await query
            .OrderByDescending(u => u.AccessedAt)
            .ToListAsync();
    }

    public async Task<Dictionary<string, int>> GetUsageStatsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null)
    {
        var usageHistory = await GetUsageHistoryAsync(featureFlagId, from, to);

        return usageHistory
            .Where(u => !string.IsNullOrEmpty(u.Variant))
            .GroupBy(u => u.Variant!)
            .ToDictionary(g => g.Key, g => g.Count());
    }

    public async Task<List<FeatureFlag>> SearchAsync(string searchTerm)
    {
        return await _context.FeatureFlags
            .Include(f => f.Rules)
            .Include(f => f.UsageHistory)
            .Where(f => f.Name.Contains(searchTerm) ||
                       f.Description.Contains(searchTerm) ||
                       f.Key.Contains(searchTerm))
            .OrderBy(f => f.Name)
            .ToListAsync();
    }
}
